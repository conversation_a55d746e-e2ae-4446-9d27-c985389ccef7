<template>
  <div class="main">
    <!-- 加载状态 -->
    <div v-if="userLoading" class="loading-container">
      <van-loading type="spinner" color="#1989fa">加载中...</van-loading>
    </div>

    <!-- 主要内容 -->
    <div v-else-if="authState !== 'auth' && $store.state.app.userInfo && $store.state.app.userInfo.username">
      <div style="height: calc(100vh - 0.71rem); overflow-y: scroll">
      <!-- {{ urlLiet }} -->
      <!-- header -->
      <div class="header">
        <div class="header-content">
          <div class="header-left">
            <img src="../assets/image/touxiang.png" alt />
          </div>
          <div class="header-right">
            <div>
              <p class="right-one">
                {{ noPassByName(this.$store.state.app.userInfo.username) }}
              </p>
              <p class="right-two">
                <span>
                  <img src="../assets/image/shenfenzheng.png" alt />
                </span>
                <span>{{ IDnumber }}</span>
              </p>
            </div>
          </div>
        </div>
        <div class="tip">
          <div>
            <notice-bar :speed="10" :text="notice" />
          </div>
        </div>
      </div>
      <!-- sec -->
      <div class="sec">
        <div class="business-add" v-if="this.serviceInfo.serviceType === '2'">
          <div
            v-if="
              !selectBusiness.length ||
              (showBusiness &&
                selectBusiness.length === hasSelectBusiness.length)
            "
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto;
            "
            @click="addBusiness" 
          >
            <img
              style="width: 0.37rem; height: 0.4rem"
              src="@/assets/image/add.png"
              alt=""
            />
            <p class="add-business">添加企业</p>
          </div>
          <div class="has-add" v-else>
            <p>
              已添加{{
                showBusiness
                  ? selectBusiness.length - hasSelectBusiness.length
                  : selectBusiness.length
              }}家企业
            </p>
            <p @click="addBusiness">添加</p>
          </div>
        </div>
        <div
          class="sec-scroll"
          id="scroll-view"
          ref="scrollView"
          @scroll="handleScroll"
          :style="{
            height: authState === 'authExpired' ? '70vh' : '45vh',
          }"
        >
          <ul>
            <li v-for="item in sceneAgreements" :key="item.agrmtName">
              <p class="title-h3 ql-editor" v-html="item.agrmtName"></p>
              <p class="ql-editor" v-html="item.agrmtDscy"></p>
            </li>
            <li v-for="(item, index) in agreements2" :key="index">
              <p class="title-h3 ql-editor" v-html="item.agrmtName"></p>
              <p class="ql-editor" v-html="item.agrmtDscy"></p>
            </li>
          </ul>
        </div>
      </div>
      <div class="agree-list" v-if="authState === 'notAuth'">
        <p style="margin-bottom: 0.16rem; color: #363a44">
          <span style="color: #e63633">*</span>授权场景列表
        </p>
        <van-checkbox-group @change="changeCheckAgreements" v-model="checked">
          <div v-for="item of agreements" :key="item.id">
            <van-checkbox
              :disabled="
                serviceInfo.serviceType === '2' && !selectBusiness.length
              "
              v-if="item.sceneLinkAgrmts"
              :name="item.sceneName"
              @click="clickAgreement(item)"
              ><span class="checkbox" v-html="item.sceneName"></span
            ></van-checkbox>
            <p class="no-agreements" v-else v-html="item.sceneName"></p>
          </div>
          <p style="height: 0.16rem"></p>
          <van-checkbox
            :disabled="
              serviceInfo.serviceType === '2' && !selectBusiness.length
            "
            v-if="agreements2.length"
            name="1"
            @click="clickAgreement('1')"
            ><p style="display: flex; flex-wrap: wrap; color: #363a44">
              本人已<span v-if="agreements2.length > 1">逐页</span>阅读<span
                class="checkbox"
                v-for="(item, index) of agreements2"
                :key="index"
              >
                《
                <p style="color: #428ffc" v-html="item.agrmtName"></p>
                》<i v-if="index !== agreements2.length - 1">、</i>
              </span>
            </p></van-checkbox
          >
          <p v-else class="no-agreements">本人已逐页阅读授权服务相关协议</p>
        </van-checkbox-group>
      </div>
      <footer-info></footer-info>
      </div>
      
      <div class="bottom">
        <div class="footer">
          <van-button plain type="info" @click="accredit(0)">拒绝</van-button>
          <van-button type="info" disabled v-if="authorization"
            >授权</van-button
          >
          <van-button type="info" v-else @click="accredit(1)">授权</van-button>
        </div>
      </div>
      <agreement
        v-if="showAgree"
        :showBtn="true"
        :ids="sceneLinkAgrmts"
        :show="showAgree"
        :agreementName="agreementName"
        :agreements="nonSceneAgrmts"
        @agreeEmit="agreeEmit"
      />
    </div>
    <div class="auth-success" v-else>
      <div class="content">
        <img src="@img/success.png" alt="" />
        <p>授权生效中</p>
        <div v-if="serviceInfo.serviceName">
          <p style="font-size: 0.14rem; color: #686b73; margin-top: 0.14rem">
            {{ serviceInfo.serviceName }}授权成功
          </p>
          <p class="tip" v-if="serviceInfo.infoLevel === '2'">
            {{ serviceInfo.serviceName }}从用户授权当日起，{{
              serviceInfo.accessDays
            }}天内有效，{{ serviceInfo.accessCount }}次使用
          </p>
          <p class="tip" v-else-if="serviceInfo.infoLevel === '1'">
            {{ serviceInfo.serviceName }}从用户授权当日起，{{
              serviceInfo.accessDays
            }}天内有效
          </p>
          <p class="tip" v-else-if="serviceInfo.infoLevel === '3'">
            {{ serviceInfo.serviceName }}一次授权，一次使用
          </p>
        </div>
      </div>
      <footer-info></footer-info>
    </div>
    
    <van-popup
      v-model="showBusiness"
      position="bottom"
      :close-on-click-overlay="false"
      :style="{ height: '50%', borderRadius: '0.08rem 0.08rem 0 0' }"
    >
      <div class="business-popup">
        <div class="business-btn">
          <p @click="cancel">取消</p>
          <p @click="confirm">确定</p>
        </div>
        <div class="business-btn2">
          <p class="select-all">全选</p>
          <van-checkbox
            v-model="all"
            shape="square"
            @click="selectAllBusiness"
          ></van-checkbox>
        </div>
        <van-checkbox-group
          v-model="selectBusiness"
          @change="handleSelectBusiness()"
        >
          <div
            v-for="(item, index) of businessList"
            :key="index"
            class="business-name"
          >
            <p>
              {{ item.fixmedinsName
              }}<span v-if="item.authStatus === 'auth'" class="authed"
                >已授权</span
              >
            </p>
            <van-checkbox
              :disabled="item.authStatus === 'auth'"
              shape="square"
              :name="item.uscc"
            ></van-checkbox>
          </div>
        </van-checkbox-group>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { Notify, Toast, Loading } from "vant";
import noticeBar from "@/components/noticeBar.vue";
import {
  authorization,
  accreditinfo,
  getScenes,
  getOtherAgreements,
  getAuthStatus,
  getServiceList,
  check,
  getUserInfo,
  getAgreements,
  getRequestId,
  reVerify,
  queryPsnOrgAuthStatus,
  faceRecognition,
  getFaceResult,
} from "@/api/index";
import common from "@/util/index";
import Vconsole from "vconsole";
import {
  randomString,
  deepCopy,
  findObjectArray,
  encrypt,
  noPassByName,
} from "@/util/index";
import agreement from "./components/agreements.vue";
import { userAuthMiddleware, getMaskedIdNumber } from "@/utils/userAuth";
export default {
  data() {
    return {
      all: false,
      showBusiness: false,
      selectBusiness: [], // 选中的公司
      hasSelectBusiness: [], // 已经授权过的公司
      businessList: [],
      notice: "",
      hasCheck: true,
      // 授权状态
      authState: "",
      // 非场景协议
      nonSceneAgrmts: [],
      // 协议ids
      sceneLinkAgrmts: "",
      showAgree: false,
      agreementName: "",
      // 组件绑定的勾选协议
      checked: [],
      // 自定义点击事件勾选的协议
      clickedAgreements: [],
      urlLiet: "",
      AuthService: "", //授权服务
      isbut: "", //是否同意
      authorization: true,
      btnRadio: "",
      //  btnRadio: "1",
      radChange: false,
      accreditList: [],
      userLoading: true, // 用户信息加载状态，初始为true
      agreements: [],
      agreements2: [],
      sceneIds: [],
      serviceIds: [],
      sceneId: "",
      serviceId: "",
      sceneIdArr: [], // 通过入口获取的场景ids
      serviceIdArr: [], // 通过入口获取的服务ids
      allSceneLinkAgrmts: [], //所有场景协议ids
      sceneAgreements: [], //所有场景协议内容
      sceneAgreementCount: 0, // 场景协议数量
      serviceInfo: {
        serviceType: "",
      },
    };
  },
  components: {
    agreement,
    noticeBar,
  },
  filters: {
    formateIDtype(val) {
      let method = "";
      switch (val) {
        // case "ID_CARD":
        case "01":
          method = "1";
          break;
        // case "MAINLAND_TRAVEL_PERMIT_FOR_HONGKONG_AND_MACAO_RESIDENTS":
        case "04":
          method = "4";
          break;
        // case "MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS":
        case "06":
          method = "5";
          break;
        default:
          method = "-1";
          break;
      }
      return method;
    },
  },
  computed: {
    // 安全地处理身份证号码脱敏
    IDnumber() {
      const userInfo = this.$store.state.app.userInfo;
      if (userInfo && userInfo.idnum && typeof userInfo.idnum === 'string') {
        return userInfo.idnum.replace(
          /^(.{1})(?:\w+)(.{1})$/,
          "$1****************$2"
        );
      }
      return "****************"; // 默认值
    }
  },
  watch: {
    selectBusiness(val) {
      if (val.length === this.businessList.length) {
        this.all = true;
      } else {
        this.all = false;
      }
    },
  },
  methods: {
    noPassByName,

    // 解析URL参数
    parseUrlParams() {
      const url = window.location.href;
      const params = {};
      if (url.indexOf('?') !== -1) {
        const str = url.split('?')[1];
        const strs = str.split('&');
        for (let i = 0; i < strs.length; i++) {
          const keyValue = strs[i].split('=');
          params[keyValue[0]] = decodeURIComponent(keyValue[1] || '');
        }
      }
      return params;
    },

    // 将原有的created逻辑提取到单独方法中
    initializePageData(userInfo) {
      // 解析URL参数获取callbackUrl
      const urlParams = this.parseUrlParams();
      if (urlParams.callbackUrl) {
        window.sessionStorage.setItem('callbackUrl', urlParams.callbackUrl);
      }
      // 获取第三方链接里面带来的场景id和服务id信息
      //从链接获取sourceData
      const sourceData = this.$route.query.sourceData;
      console.log(sourceData, "sourceData");
      if (sourceData) {
        window.sessionStorage.setItem("sourceData", sourceData);
        console.log(window.sessionStorage.getItem("sourceData"), "window.sessionStorage.getItem(sourceData)");
      }
      if (window.sessionStorage.getItem("sourceData")) {
        console.log("打印 handleGetAuthStatusForBusiness ")
        const obj = JSON.parse(
          decodeURIComponent(window.sessionStorage.getItem("sourceData"))
        );

        // 解析场景ID和服务ID
        if (obj.se) {
          if (obj.se.indexOf(",") !== -1) {
            this.sceneIdArr = obj.se.split(",");
          } else {
            this.sceneId = obj.se;
            this.sceneIdArr = [obj.se];
          }
        }

        if (obj.sv) {
          if (obj.sv.indexOf(",") !== -1) {
            this.serviceIdArr = obj.sv.split(",");
          } else {
            this.serviceId = obj.sv;
            this.serviceIdArr = [obj.sv];
          }
        }

        console.log(this.sceneIdArr, this.serviceIdArr, "sceneIdArr");

        // 获取授权状态和其他数据
        this.handleGetAuthStatusForBusiness();
        this.handleGetServiceList();
        this.loadSceneData();
        this.loadNonSceneAgreements();
      } else {
        console.log("没有sourceData，跳转到首页");
        this.$router.push('/');
      }
    },

    // 加载场景数据
    loadSceneData() {
      this.createTime = new Date().getTime();
      // 获取场景
      getScenes({
        logTraceID: randomString(),
        status: "0",
        sceneIds: this.sceneIdArr,
        serviceIds: this.serviceIdArr,
      }).then(res => {
        this.agreements = res.records;
        for (const item of this.agreements) {
          item.check = false;
        }
        this.allSceneLinkAgrmts = [];
        this.sceneAgreementCount = 0;
        for (const item of this.agreements) {
          if (item.sceneLinkAgrmts) {
            this.allSceneLinkAgrmts = this.allSceneLinkAgrmts.concat(
              item.sceneLinkAgrmts.split(",")
            );
            this.sceneAgreementCount++;
          }
        }
        getAgreements({
          // 协议id为空主动塞入0
          agrmtIds: this.allSceneLinkAgrmts.length
            ? this.allSceneLinkAgrmts
            : ["0"],
          logTraceID: randomString(),
        }).then(res => {
          this.sceneAgreements = res.records;
        });
      });
    },

    // 加载非场景协议
    loadNonSceneAgreements() {
      getOtherAgreements({
        logTraceID: randomString(),
        sceneIds: this.sceneIdArr,
        serviceIds: this.serviceIdArr,
      }).then(res => {
        if (res.data) {
          this.agreements2 = JSON.parse(res.data).nonSceneAgrmts;
        } else {
          // 当接口获取不到信息时使用假数据
          this.agreements2 = this.mockAgreements2;
        }
      });
    },
    cancel() {
      this.selectBusiness = [];
      this.showBusiness = false;
    },
    confirm() {
      for (const item of this.hasSelectBusiness) {
        if (this.selectBusiness.indexOf(item) !== -1) {
          this.selectBusiness.splice(this.selectBusiness.indexOf(item), 1);
        }
      }
      this.showBusiness = false;
    },
    selectAllBusiness() {
      if (this.selectBusiness.length === this.businessList.length) {
        this.selectBusiness = JSON.parse(
          JSON.stringify(this.hasSelectBusiness)
        );
        console.log(this.selectBusiness, "this.selectBusiness");
      } else {
        this.selectBusiness = [];
        for (const item of this.businessList) {
          this.selectBusiness.push(item.uscc);
        }
      }
    },
    handleSelectBusiness() {
      // if (this.selectBusiness.length === this.businessList.length) {
      //   this.all = true;
      // } else {
      //   this.all = false;
      // }
    },
    addBusiness() {
      if (this.businessList.length === 0) {
      Toast("暂无数据"); // 弹出提示框
    } else {
      this.hasSelectBusiness = [];
      for (const item of this.businessList) {
        if (item.authStatus === "auth") {
          this.selectBusiness.push(item.uscc);
          this.hasSelectBusiness.push(item.uscc);
        }
      }
      this.showBusiness = true;
    }

    },
    goBack() {
      this.$router.push({
        path: "/home",
      });
    },
    // 获取公司授权状态
    handleGetAuthStatusForBusiness() {
      console.log("打印 handleGetAuthStatusForBusiness ")
      queryPsnOrgAuthStatus({
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
        sceneIds: this.sceneIdArr,
        serviceIds: this.serviceIdArr,
        logTraceID: randomString(),
      }).then(res => {
        if(res.data){        
          this.businessList = JSON.parse(res.data);
          let allAuthorized = true; // 新增变量来跟踪是否所有公司都授权成功
          for (const item of this.businessList) {
            if (item.authStatus !== "auth") {
              allAuthorized = false; // 如果有任何一家公司未授权，设置为 false
              break;
            }
          }
          this.authState = allAuthorized ? "auth" : "notAuth"; // 根据是否所有公司都授权成功来设置状态
        }
      });
    },
    // 获取个人授权状态码
    handleGetAuthStatus() {
      console.log("打印 handleGetAuthStatus ")
      const data = {
        certNo: encrypt(this.$store.state.app.userInfo.idnum),
        sceneIds: this.sceneIdArr,
        serviceIds: this.serviceIdArr,
        // sceneId: "sceneId2",
        // serviceId: "serviceId1",
        logTraceID: randomString(),
      };
      getAuthStatus(data).then(res => {
        const urlParams = this.parseUrlParams();
      var callbackUrl = urlParams.callbackUrl || window.sessionStorage.getItem("callbackUrl") || "";
        var url = "";
        const status = JSON.parse(res.data);
        // 已授权跳回第三方回调地址
        if (status.authStatus === "auth" && callbackUrl) {
          if (callbackUrl.indexOf("?") !== -1) {
            url =
              callbackUrl.slice(0, callbackUrl.indexOf("?") + 1) +
              "auth=true&" +
              callbackUrl.slice(callbackUrl.indexOf("?") + 1);
            window.location.href = decodeURIComponent(url);
          } else {
            window.location.href = decodeURIComponent(
              `${callbackUrl}?auth=true`
            );
          }
        } else {
          // 不是第三方由自己页面判断显示已授权页面还是授权成功页面
          this.authState = status.authStatus;
        }
        if (this.authState === "authExpired") this.authorization = false;
        if (status === "notAuth") {
          console.log("没授权");
        } else if (status === "authExpired") {
          console.log("授权失效");
        } else {
          console.log("授权生效中");
        }
      });
    },
    // 获取服务信息
    handleGetServiceList() {
      getServiceList({
        current: 1,
        size: 10,
        logTraceID: randomString(),
        serviceIds: this.serviceIdArr,
      }).then(res => {
        this.serviceInfo = res.records[0];
        if (this.serviceInfo.infoLevel === "2") {
          this.notice = `${this.serviceInfo.serviceName}从用户授权当日起，${this.serviceInfo.accessDays}天内有效，${this.serviceInfo.accessCount}次使用`;
        } else if (this.serviceInfo.infoLevel === "1") {
          this.notice = `${this.serviceInfo.serviceName}从用户授权当日起，${this.serviceInfo.accessDays}天内有效`;
        } else if (this.serviceInfo.infoLevel === "3") {
          this.notice = `${this.serviceInfo.serviceName}一次授权，一次使用`;
        }
        if (this.serviceInfo.serviceType === "1") {
          this.handleGetAuthStatus();
        } else {
          this.handleGetAuthStatusForBusiness();
        }
      });
    },
    // 协议自定义点击事件
    clickAgreement(val) {
      if (this.serviceInfo.serviceType === "2" && !this.selectBusiness.length) {
        Notify({ type: "warning", message: "请先添加企业" });
        return;
      }
      var index;
      if (val == 1) {
        index = this.clickedAgreements.indexOf(val);
        this.agreementName = val;
        this.nonSceneAgrmts = deepCopy(this.agreements2);
      } else {
        index = this.clickedAgreements.indexOf(val.sceneName);
        this.agreementName = val.sceneName;
        // this.nonSceneAgrmts = [];
      }
      if (index === -1) {
        // 场景协议ids
        this.sceneLinkAgrmts = val.sceneLinkAgrmts;
        this.showAgree = true;
      } else {
        this.clickedAgreements.splice(index, 1);
      }
      this.checked = this.clickedAgreements;
    },
    agreeEmit(val, agree) {
      this.showAgree = false;
      if (agree) {
        this.clickedAgreements.push(val);
      }
      this.checked = this.clickedAgreements;
    },

    // 获取授权协议
    getWarranty() {
      accreditinfo(1).then(res => {
        this.accreditList = JSON.parse(
          JSON.parse(res.data.data).limitAuthContent
        );
      });
    },
    // 授权接口
    getAuthoCall() {
      let params = {
        authMethod: "web",
        name: encrypt(this.$store.state.app.userInfo.username),
        serviceIds: this.serviceIdArr,
        sceneIds: this.sceneIdArr,
        logTraceID: randomString(),
      };
      if (this.selectBusiness.length) {
        if (this.selectBusiness.length === 1) {
          params.certNo = encrypt(this.selectBusiness[0]);
        } else {
          for (const item of this.selectBusiness) {
            params.certNo = params.certNo
              ? params.certNo + "," + encrypt(item)
              : encrypt(item);
          }
        }
      } else {
        params.certNo = encrypt(this.$store.state.app.userInfo.idnum);
      }
      // let formData = new FormData();
      // for (const key in params) {
      //   formData.append(key, params[key]);
      // }
      var callbackUrl = window.sessionStorage.getItem("callbackUrl") || "";
      var url = "";
      authorization(params).then(res => {
        if (res.status === "200") {
          if (callbackUrl) {
            if (callbackUrl.indexOf("?") !== -1) {
              url =
                callbackUrl.slice(0, callbackUrl.indexOf("?") + 1) +
                "auth=true&" +
                callbackUrl.slice(callbackUrl.indexOf("?") + 1);
              window.location.href = decodeURIComponent(url);
            } else {
              window.location.href = decodeURIComponent(
                `${callbackUrl}?auth=true`
              );
            }
          } else {
            this.$router.push({
              path: "/authSuccess",
              query: {
                serviceInfo: this.serviceInfo,
              },
            });
          }
          Notify({ type: "success", message: "授权成功" });
        } else {
          if (callbackUrl) {
            if (callbackUrl.indexOf("?") !== -1) {
              url =
                callbackUrl.slice(0, callbackUrl.indexOf("?") + 1) +
                "auth=false&" +
                callbackUrl.slice(callbackUrl.indexOf("?") + 1);
              window.location.href = decodeURIComponent(url);
            } else {
              window.location.href = decodeURIComponent(
                `${callbackUrl}?auth=false`
              );
            }
          } else {
            Notify({ type: "warning", message: res.message });
          }
        }
      });
    },
    // 判断是否滚动底部
    handleScroll(e) {
      // const { scrollTop, clientHeight, scrollHeight } = e.target;
      // if (scrollTop + clientHeight === scrollHeight) {
      //   this.btnRadio = "1";
      //   this.$nextTick(res => {
      //     if (this.authState === "authExpired") this.authorization = false;
      //   });
      // }
    },
    // 点击隐私直达
    privacy() {
      this.$router.push({
        path: "/agreement",
      });
    },
    // 授权
    accredit(e) {
      // this.getAuthoCall();
      if (e == 0) {
        this.isbut = 0;
        Notify({ type: "primary", message: "授权失败" });
      } 
      else if (
        this.$options.filters.formateIDtype(
          this.$store.state.app.userInfo.idType
        ) === "-1"
      ) {
        //Notify打印出当前证件类型
        Notify({ type: "primary", message: "证件类型不支持身份核验,当前证件类型为：" + this.$options.filters.formateIDtype(this.$store.state.app.userInfo.idType) });
        // Notify({ type: "warning", message: "证件类型不支持身份核验" });
      }
       else {
        this.isbut = 1;
        console.log(this.$store.state.app.userInfo, "userInfo");
        const data = {
          certNo: this.$store.state.app.userInfo.idnum,
          certName: this.$store.state.app.userInfo.username,
          certType: this.$options.filters.formateIDtype(
            this.$store.state.app.userInfo.idType
          ),
        };
        console.log(data, "获取id");
        this.getAuthoCall();
        // getRequestId({
        //   logTraceID: randomString(),
        //   data: encrypt(JSON.stringify(data)),
        // }).then(res => {
        //   console.log(res);
        //   console.log(
        //     {
        //       accessKey: "BCDSGA_e4d134d2a458d6f671121f82d2143fbe",
        //       requestId: JSON.parse(res.data).requestId,
        //     },
        //     "人脸"
        //   );
        //   // 使用新的第三方人脸识别接口替代ZWJSBridge.authentication
        //   this.performFaceRecognition({
        //     accessKey: "BCDSGA_e4d134d2a458d6f671121f82d2143fbe",
        //     requestId: JSON.parse(res.data).requestId,
        //     certNo: this.$store.state.app.userInfo.idnum,
        //     certName: this.$store.state.app.userInfo.username,
        //     certType: this.$options.filters.formateIDtype(
        //       this.$store.state.app.userInfo.idType
        //     ),
        //   });
        // });
      }
    },
    // radio取消/选中
    changRad() {
      this.radChange = true;
      this.authorization = true;
    },
    changeCheckAgreements(val) {
      if (this.agreements2.length) {
        if (
          val.length === this.sceneAgreementCount + 1 &&
          val.indexOf("1") !== -1
        ) {
          this.authorization = false;
        } else {
          this.authorization = true;
        }
      } else {
        if (val.length === this.sceneAgreementCount) {
          this.authorization = false;
        } else {
          this.authorization = true;
        }
      }

      var arr = findObjectArray(this.agreements, this.checked, "sceneName");
      this.sceneIds = [];
      for (const item of arr) {
        this.sceneIds.push(item.sceneId);
      }
    },

    // 新的人脸识别方法（替代ZWJSBridge.authentication）
    // performFaceRecognition(params) {
    //   console.log("开始人脸识别", params);

    //   // 调用第三方人脸识别接口
    //   faceRecognition({
    //     logTraceID: randomString(),
    //     accessKey: params.accessKey,
    //     requestId: params.requestId,
    //     certNo: params.certNo,
    //     certName: params.certName,
    //     certType: params.certType,
    //   })
    //     .then(response => {
    //       console.log("人脸识别接口响应", response);

    //       if (response.status === "200") {
    //         const result = JSON.parse(response.data);

    //         // 根据实际接口返回结构调整
    //         if (result.success || result.pass) {
    //           console.log("人脸识别成功");

    //           // 验证人脸识别结果
    //           reVerify({
    //             logTraceID: randomString(),
    //             requestId: params.requestId,
    //           }).then(verifyResult => {
    //             console.log("验证结果", verifyResult);
    //             if (verifyResult.status === "200") {
    //               this.getAuthoCall();
    //             } else {
    //               Notify({ type: "warning", message: "身份验证失败" });
    //             }
    //           }).catch(error => {
    //             console.error("验证失败", error);
    //             Notify({ type: "warning", message: "身份验证异常" });
    //           });
    //         } else {
    //           Notify({ type: "warning", message: result.message || "人脸识别失败" });
    //         }
    //       } else {
    //         Notify({ type: "warning", message: response.message || "人脸识别服务异常" });
    //       }
    //     })
    //     .catch(error => {
    //       console.error("人脸识别异常", error);
    //       Notify({ type: "warning", message: "人脸识别服务不可用，请稍后重试" });
    //     });
    // },
  },
  async created() {
    this.createTime = new Date().getTime();
    this.userLoading = true;

    //使用用户认证中间件检测token并获取用户信息
    await userAuthMiddleware({
      onSuccess: (userInfo) => {
        console.log("用户认证成功:", userInfo);
        this.userLoading = false;

        if (userInfo && userInfo.idnum) {
          console.log(encrypt(userInfo.idnum), "idnum");
          // 用户信息获取成功后，继续原有逻辑
          this.initializePageData(this.$store.state.app.userInfo);
        }
      },
      onError: (error) => {
        console.error("用户认证失败:", error);
        this.userLoading = false;
        Toast.fail("用户认证失败，请重新登录");

        // setTimeout(() => {
        //   this.$router.push('/');
        // }, 2000);
      },
      required: true
    });
  },
  mounted() {
    var vc = new Vconsole();
    console.log(vc);
        
    // 页面成功后移除sourceData为了之后判断页面跳转
    window.setTimeout(() => {
      window.sessionStorage.removeItem("sourceData");
    }, 10000);
    this.mountedTime = new Date().getTime();
    common.changeTitle("医保个人授权");
    this.$buryPoint(this.mountedTime - this.createTime);
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/css/public.scss";
.main {
  width: 100%;
  height: 100vh;
  overflow:hidden;
  background: #f5f5f5;
  .bottom {
    height: 0.71rem;
    // position: fixed;
    // bottom: 0;
    // z-index: 100;
    width: 100%;
    box-sizing: border-box;
    padding: 0 0.16rem;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.28);
    display: flex;
    justify-content: center;
    align-items: center;
    .footer {
      display: flex;
      width: 100%;
      justify-content: space-between;
      .van-button {
        width: 1.68rem;
        height: 0.48rem;
        border-radius: 0.24rem;
        @include font_size_18_24($fontSize-18);
      }
    }
    ::v-deep.van-radio__label {
      font-size: 0.15rem;
      color: black;
      vertical-align: middle;
      span {
        color: #266ffc;
        vertical-align: middle;
      }
    }
    ::v-deep.van-radio-group {
      padding: 0.1rem;
      margin-bottom: 20px;
    }
    .footer-title {
      padding-top: 0.2rem;
      font-size: 0.12rem;
      text-align: center;
      letter-spacing: 0.02rem;
      height: 0.115rem;
      color: #686b73;
      font-family: AlibabaPuHuiTiR;
    }
  }
  ::v-deep.agree-list {
    padding: 0 0.16rem;
    margin-top: 0.68rem;
    max-height: calc(55vh - 2.05rem);
    overflow-y: scroll;
    p {
      @include font_size_14_20($fontSize-14);
    }
    .checkbox {
      color: #428ffc;
      @include font_size_14_20($fontSize-14);
      display: flex;
      align-items: center;
      margin-bottom: 0.12rem;
      line-height: 0.2rem;
    }
    .van-checkbox__icon--round .van-icon {
      margin-top: 0.01rem;
      width: 0.14rem;
      height: 0.14rem;
      display: flex;
      justify-content: center;
      align-items: center;
      &::before {
        width: 0.1rem;
        height: 0.1rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .agree-list::-webkit-scrollbar {
    width: 0;
  }
  .header {
    width: 100%;
    height: 1.68rem;
    background: url("../assets/image/beijing1.png") no-repeat center;
    background-size: cover;
    background-size: 100% 100%;
    // display: flex;
    // justify-content: center;
    .header-content {
      box-sizing: border-box;
      width: 100%;
      height: 0.84rem;
      display: flex;
      padding: 0.05rem 0.16rem;
    }
    .header-left {
      width: 0.47rem;
      height: 0.47rem;
      box-sizing: border-box;
      img {
        width: 100%;
        height: 100%;
        margin-top: -0.5rem;
        vertical-align: middle;
      }
    }
    .header-right {
      display: flex;
      flex: 1;
      justify-content: space-between;
      // height: 1rem;
      margin-top: 0.1rem;
      margin-left: 0.1rem;
      vertical-align: middle;
      .right-one {
        @include font_size_18_24($fontSize-18);
        height: 0.18rem;
        color: #ffffff;
        margin-bottom: 0.08rem;
      }
      .right-two {
        color: #ffffff;
        @include font_size_13_18($fontSize-13);
        img {
          width: 0.15rem;
          height: 0.12rem;
          margin-right: 0.05rem;
          vertical-align: middle;
        }
      }
      .auth-manage {
        width: 0.72rem;
        text-align: center;
        line-height: 0.28rem;
        border: 1px solid #ffffff;
        border-radius: 0.14rem;
        @include font_size_14_20($fontSize-14);
        color: white;
      }
    }
    .tip {
      box-sizing: border-box;
      width: 100%;
      padding: 0 0.16rem;
      div {
        display: flex;
        // align-content: center;
        align-items: center;
        height: 0.27rem;
        background: #e7f3ff;
        border-radius: 0.08rem;
        img {
          width: 0.11rem;
          height: 0.1rem;
          margin-left: 0.12rem;
        }
        p {
          line-height: 0.27rem;
          @include font_size_12_18($fontSize-12);
          color: #428ffc;
          transform: scale(0.92);
        }
      }
    }
  }
  .sec-scroll::-webkit-scrollbar {
    width: 0;
  }
  .sec {
    margin: -0.48rem auto;
    width: 3.45rem;
    border-radius: 0.1rem;
    .sec-scroll {
      border-radius: 0.1rem;
      height: 45vh;
      background: #ffffff;
      overflow-y: scroll;
      ul {
        padding: 0.16rem;
        li {
          color: #666666;
          @include font_size_14_20($fontSize-14);
          line-height: 0.21rem;
          &:nth-of-type(1) {
            .title-h3 {
              margin-top: 0;
            }
          }
        }
        // :first-child {
        //   color: #333333;
        //   font-size: 0.16rem;
        //   margin-bottom: 0.15rem;
        // }
      }
    }
    .add-business {
      @include font_size_14_20($fontSize-14);
      font-weight: 500;
      color: #363a44;
    }
  }
  .van-checkbox {
    align-items: flex-start;
  }
  .auth-success {
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 100%;
      height: 4rem;
      background: white;
      img {
        width: 1.44rem;
        height: 1.44rem;
        margin-bottom: 0.24rem;
      }
      p {
        color: #363a44;
        font-weight: 400;
        font-size: 0.2rem;
        text-align: center;
      }
    }
    .footer {
      width: 100%;
      height: 0.71rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.28);
      // position: fixed;
      // bottom: 0;
      .footer-one {
        padding: 0.16rem;
        width: 90%;
        height: 0.48rem;
        margin: 0 auto;
        vertical-align: middle;
        position: relative;
        .van-button {
          position: absolute;
          width: 90%;
          border-radius: 0.24rem;
          height: 0.48rem;
          vertical-align: middle;
          line-height: 0.48rem;
          @include font_size_16_22($fontSize-16);
        }
      }
    }
    .tip {
      font-size: 0.28rem;
      color: #428ffc;
      line-height: 0.4rem;
      margin-top: 0.14rem;
    }
  }
  .title-h3 {
    margin: 0.3rem 0;
  }
  .no-agreements {
    position: relative;
    padding-left: 24px;
    margin-bottom: 0.12rem;
    &::before {
      content: "";
      position: absolute;
      left: 3px;
      top: 50%;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #b3b5b9;
      transform: translate(0%, -50%);
    }
  }
  .business-popup {
    .business-btn {
      height: 0.44rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0rem 0.25rem;
      & > p {
        @include font_size_16_22($fontSize-16);
        font-weight: 500;
        &:nth-of-type(1) {
          color: #999999;
        }
        &:nth-of-type(2) {
          color: #3a5ce0;
        }
      }
    }
    .business-btn2 {
      height: 0.42rem;
      background: #f3f3f3;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0rem 0.25rem;
      .select-all {
        @include font_size_16_22($fontSize-16);
        color: #363a44;
      }
    }
    .business-name {
      padding: 0rem 0.25rem;
      height: 0.51rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      & > p {
        height: 0.25rem;
        @include font_size_16_22($fontSize-16);
        color: #363a44;
        display: flex;
        align-items: center;
      }
    }
  }
  .business-add {
    height: 0.49rem;
    width: 100%;
    border-radius: 0.1rem;
    margin-bottom: 0.1rem;
    background-color: #fff;
    display: flex;
    align-items: center;
    // justify-content: center;
    margin-right: 0.06rem;
  }
  .authed {
    display: flex;
    margin-left: 0.1rem;
    width: 0.38rem;
    height: 0.15rem;
    background: #428ffc;
    border-radius: 2px;
    font-size: 0.1rem;
    color: #ffffff;
    justify-content: center;
    align-items: center;
  }
  .has-add {
    display: flex;
    align-items: center;
    p {
      &:nth-of-type(1) {
        font-weight: bolder;
        @include font_size_14_20($fontSize-14);
        color: #363a44;
        margin: 0 0.1rem 0 0.17rem;
      }
      &:nth-of-type(2) {
        width: 0.25rem;
        height: 0.15rem;
        border-radius: 2px;
        border: 1px solid #428ffc;
        font-size: 0.1rem;
        color: #428ffc;
        text-align: center;
        line-height: 0.15rem;
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}
</style>
