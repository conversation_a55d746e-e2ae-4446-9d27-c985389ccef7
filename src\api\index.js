// import service from "@/util/https";
import { httpReq2, httpReq, httpReq3 } from "@/util/https";

// export function check(data) {
//   return httpReq3.getQuery({
//     url: "check",
//     data,
//   });
// }

// // get请求参数是存放在params
// export function GetCode(data) {
//   return httpReq.getQuery({
//     url: "topics",
//     data,
//   });
// }
// // 个人医保授权
// export function authorization(data) {
//     return httpReq.postFile({
//         url: "/user/auth",
//         data,
//     });
// }
// // 个人医保服务调用记录
// export function authoCall(data) {
//   return httpReq.postJson({
//     url: "queryService",
//     data,
//   });
// }

// 个人医保服务调用记录（去重）
// export function authoCalls(data) {
//     return httpReq.postJson({
//         url: "/user/queryUniServiceLog",
//         data,
//     });
// }

// // 个人医保场景调用记录
// export function authoChannel(data) {
//   return httpReq.postJson({
//     url: "queryScene",
//     data,
//   });
// }

// // 个人医保服务授权记录（服务场景一对一 相同服务可能有多条对应不同的场景）
// export function getAuthList(data) {
//   return httpReq.postJson({
//     url: "/user/queryAuth",
//     data,
//   });
// }

// // 个人医保场景调用记录（去重）
// export function authoChannels(data) {
//   return httpReq.postJson({
//     url: "queryUniSceneLog",
//     data,
//   });
// }
// // 解除服务授权（已废弃）
// export function channService(data) {
//   return httpReq.postJson({
//     url: "evictService",
//     data,
//   });
// }
// //解除场景授权（已废弃）
// export function chanChannel(data) {
//   return httpReq.postJson({
//     url: "evictScene",
//     data,
//   });
// }
// // 解除授权（新）
// export function evictAuth(data) {
//     return httpReq.postJson({
//         url: "/user/evictAuth",
//         data,
//     });
// }
// // 获取授权协议
// export function accreditinfo(data) {
//     return httpReq.postJson({
//         url: `/user/getLimit/${authLimitId}`,
//         data,
//     });
// }

// // 获取所有场景列表
// export function getScenes(data) {
//     return httpReq.postJson({
//         url: "/manage/scene/mnQueryScene",
//         data,
//     });
// }

// // 获取协议内容
// export function getAgreements(data) {
//     return httpReq.postJson({
//         url: "/manage/agrmt/mnQueryAgrmt",
//         data,
//     });
// }

// // 获取场景以外的协议
// export function getOtherAgreements(data) {
//     return httpReq.postJson({
//         url: "/user/queryNonSceneAgrmts",
//         data,
//     });
// }

// // 获取某个服务下的所有协议
// export function queryAuthedAgrmts(data) {
//   return httpReq.postJson({
//     url: "queryAuthedAgrmts",
//     data,
//   });
// }

// // 根据服务查询场景调用记录
// export function querySceneCallLog(data) {
//   return httpReq.postJson({
//     url: "queryCallLog",
//     data,
//   });
// }

// // 获取授权状态码
// export function getAuthStatus(data) {
//     return httpReq.postJson({
//         url: "/user/getAuthStatus",
//         data,
//     });
// }

// // 获取服务列表
// export function getServiceList(data) {
//     return httpReq.postJson({
//         url: "/manage/service/mnQueryServ",
//         data,
//     });
// }

// // 获取浙里办用户体系(非微信小程序)
// export function getUserInfo(data) {
//   return httpReq2.postJson({
//     url: "zlbSso",
//     data,
//   });
// }

// // 获取浙里办用户体系(微信小程序)
// export function getUserInfoForWechat(data) {
//   return httpReq2.postJson({
//     url: "zlbWechatSso",
//     data,
//   });
// }

// //查看协议
// export function chanLimitinfo(data) {
//   return httpReq.postJson({
//     url: "getLimitinfo",
//     data,
//   });
// }

// //获取军人名单
// export function getNameList(data) {
//   return httpReq.postJson({
//     url: "getNameList",
//     data,
//   });
// }

// //获取军人信息
// export function getVaSettleInfo(data) {
//   return httpReq.postJson({
//     url: "queryVaSettleInfo",
//     data,
//   });
// }
// //查询法人名下所有企业授权状态
// export function queryPsnOrgAuthStatus(data) {
//     return httpReq.postJson({
//         url: "/org/queryPsnOrgAuthStatus",
//         data,
//     });
// }
// // 查询法人名下所有企业
// export function queryMedinsForCertno(data) {
//     return httpReq.postJson({
//         url: "/org/queryMedinsForCertno",
//         data,
//     });
// }

import service, { service2 } from "@/util/https";
export function test(data) {
  return service({
    url: "zlbWechatSso.COPY.oZcaOHmM",
    data,
    type: "POST",
  });
}

export function check(data) {
  return service({
    url: "check",
    data,
    type: "GET",
  });
}

// get请求参数是存放在params
export function GetCode(data) {
  return service({
    url: "topics",
    data,
    type: "GET",
  });
}
// 个人医保授权
export function authorization(data) {
  // 统一使用JSON格式，避免FormData在移动设备上的兼容性问题
  // 既然桌面浏览器使用JSON格式能成功，说明后端支持JSON格式
  return service({
    url: "auth",
    data: data,
    type: "POST",
    headers: {
      'Content-Type': 'application/json', // 统一使用JSON格式
      'Cookie': 'XSRF-TOKEN=1',
    }
  });
}
// 个人医保服务调用记录
export function authoCall(data) {
  return service({
    url: "queryService",
    data,
    type: "POST",
  });
}

// 个人医保服务调用记录（去重）
export function authoCalls(data) {
  return service({
    url: "queryUniServiceLog",
    data,
    type: "POST",
  });
}

// 个人医保场景调用记录
export function authoChannel(data) {
  return service({
    url: "queryScene",
    data,
    type: "POST",
  });
}

// 个人医保服务授权记录（服务场景一对一 相同服务可能有多条对应不同的场景）
export function getAuthList(data) {
  return service({
    url: "queryAuth",
    data,
    type: "POST",
  });
}

// 个人医保场景调用记录（去重）
export function authoChannels(data) {
  return service({
    url: "queryUniSceneLog",
    data,
    type: "POST",
  });
}
// 解除服务授权（已废弃）
export function channService(data) {
  return service({
    url: "evictService",
    data,
    type: "POST",
  });
}
//解除场景授权（已废弃）
export function chanChannel(data) {
  return service({
    url: "evictScene",
    data,
    type: "POST",
  });
}
// 解除授权（新）
export function evictAuth(data) {
  return service({
    url: "evictAuth",
    data,
    type: "POST",
  });
}
// 获取授权协议
export function accreditinfo(data) {
  return service({
    url: `getLimit/${authLimitId}`,
    data,
    type: "POST",
  });
}

// 获取所有场景列表
export function getScenes(data) {
  return service({
    url: "mnQueryScene",
    data,
    type: "POST",
  });
}

// 获取协议内容
export function getAgreements(data) {
  return service({
    url: "mnQueryAgrmt",
    data,
    type: "POST",
  });
}

// 获取场景以外的协议
export function getOtherAgreements(data) {
  return service({
    url: "queryNonSceneAgrmts",
    data,
    type: "POST",
  });
}

// 获取某个服务下的所有协议
export function queryAuthedAgrmts(data) {
  return service({
    url: "queryAuthedAgrmts",
    data,
    type: "POST",
  });
}

// 根据服务查询场景调用记录
export function querySceneCallLog(data) {
  return service({
    url: "queryCallLog",
    data,
    type: "POST",
  });
}

// 获取授权状态码
export function getAuthStatus(data) {
  return service({
    url: "getAuthStatus",
    data,
    type: "POST",
  });
}

// 获取服务列表
export function getServiceList(data) {
  return service({
    url: "mnQueryServ",
    data,
    type: "POST",
  });
}

// 获取浙里办用户体系(非微信小程序);
export function getUserInfo(data) {
  return service({
    url: "zlbSso",
    data,
    type: "POST",
  });
}

// 获取浙里办用户体系(微信小程序)
export function getUserInfoForWechat(data) {
  return service({
    url: "zlbWechatSso",
    data,
    type: "POST",
  });
}


// 通过token获取用户信息（使用csaf接口）
export function getUserInfoByTokenFromCsaf(data) {
  return service2({
    type: "POST",
    url: "user/zlyb/getUserByTk",
    data: data,
    headers: {
      "Content-Type": "application/json",
      "X-XSRF-TOKEN": "1",
    },
  });
}

//查看协议
export function chanLimitinfo(data) {
  return service({
    url: "getLimitinfo",
    data,
    type: "POST",
  });
}

//获取军人名单
export function getNameList(data) {
  return service({
    url: "getNameList",
    data,
    type: "POST",
  });
}

//获取军人信息
export function getVaSettleInfo(data) {
  return service({
    url: "queryVaSettleInfo",
    data,
    type: "POST",
  });
}

//查询法人名下所有企业授权状态
export function queryPsnOrgAuthStatus(data) {
  return service({
    url: "queryPsnOrgAuthStatus",
    data,
    type: "POST",
  });
}

// 查询法人名下所有企业
export function queryMedinsForCertno(data) {
  return service({
    url: "queryMedinsForCertno",
    data,
    type: "POST",
  });
}

// 获取requestId
export function getRequestId(data) {
  return service({
    url: "getRequestId",
    data,
    type: "POST",
  });
}

//通过 requestId 获取身份认证结果
export function reVerify(data) {
  return service({
    url: "reVerify",
    data,
    type: "POST",
  });
}

// // 获取requestId
// export function getRequestId(data) {
//   return httpReq3.postJson({
//     url: "/mamp/getRequestId",
//     data,
//   });
// }

// //通过 requestId 获取身份认证结果
// export function reVerify(data) {
//   return httpReq3.postJson({
//     url: "/mamp/reVerify",
//     data,
//   });
// }
